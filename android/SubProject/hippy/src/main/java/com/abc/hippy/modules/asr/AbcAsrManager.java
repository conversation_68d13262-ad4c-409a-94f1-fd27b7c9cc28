package com.abc.hippy.modules.asr;

import android.os.Build;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.content.Context;
import android.telephony.PhoneStateListener;
import android.telephony.TelephonyManager;

import com.abc.common.utils.ContextHolder;
import com.abc.common.utils.LogUtils;
import com.abc.hippy.modules.HostHippyMessageBridge;
import com.abc.hippy.modules.asr.model.AsrConfig;
import com.abc.hippy.modules.asr.model.AsrState;
import com.abc.hippy.utils.ArgumentUtils;
import com.tencent.mtt.hippy.common.HippyArray;
import com.tencent.mtt.hippy.common.HippyMap;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Map;
import java.util.Set;
import java.util.stream.IntStream;

/**
 * ABC ASR 管理器
 * 负责协调音频录制和 WebSocket 通信
 */
public class AbcAsrManager {
    private static final String TAG = "AbcAsrManager";

    private AsrConfig config;
    private AbcAudioRecorder audioRecorder;
    private AbcAsrWebSocketClient webSocketClient;
    private PhoneStateListener phoneStateListener;
    private TelephonyManager telephonyManager;

    private HandlerThread workerThread;
    private Handler workerHandler;
    private Handler mainHandler;

    public AbcAsrManager() {
        mainHandler = new Handler(Looper.getMainLooper());
        initWorkerThread();
    }

    /**
     * 初始化工作线程
     */
    private void initWorkerThread() {
        workerThread = new HandlerThread("AsrManagerThread");
        workerThread.start();
        workerHandler = new Handler(workerThread.getLooper());
    }


    /**
     * 初始化socket连接
     */
    public boolean initConnection(AsrConfig config) {
        this.config = config;

        LogUtils.d(TAG, "Initializing ASR connection with config: " + config.toString());

        workerHandler.post(() -> {
            try {
                // 1. 初始化 WebSocket 客户端
                webSocketClient = new AbcAsrWebSocketClient(config);

                // 2. 连接 WebSocket
                webSocketClient.connect();
            } catch (Exception e) {
                LogUtils.e(TAG, "Failed to initialize connection: " + e.getMessage());
                notifyError("INIT_FAILED", "初始化连接失败: " + e.getMessage());
            }
        });

        return true;
    }

    /**
     * 开始录音（需要先建立连接）
     */
    public boolean startRecording(String event) {
        LogUtils.d(TAG, "Starting audio recording");

        workerHandler.post(() -> {
            try {
                initAudioRecorder(event);
            } catch (Exception e) {
                LogUtils.e(TAG, "Failed to start recording: " + e.getMessage());
                notifyError("RECORD_FAILED", "开始录音失败: " + e.getMessage());
            }
        });

        return true;
    }

    /**
     * 初始化音频录制器
     */
    private void initAudioRecorder(String event) {
        if (audioRecorder != null) {
            LogUtils.w(TAG, "Audio recorder already initialized, skipping");
            return;
        }

        LogUtils.d(TAG, "Initializing audio recorder");

        audioRecorder = new AbcAudioRecorder(config);
        audioRecorder.setAudioDataCallback(new AbcAudioRecorder.AudioDataCallback() {
            @Override
            public void onAudioData(byte[] audioData, int length) {
                // 音频数据回调，发送到 WebSocket
                if (webSocketClient != null) {
                    // 只发送实际长度的数据
                    Byte[] actualData = null;
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        actualData = IntStream.range(0,length)
                                .boxed()
                                .map(index -> audioData[index])
                                .toArray(Byte[]::new);
                    }
                    webSocketClient.sendSocketBuffer(event, actualData);
                }
            }

            @Override
            public void onWaveformData(byte[] waveform, int length) {
                final int targetDataPoints = 6;
                int samplesPerPoint = length / 2 / targetDataPoints;  // 注意：waveform是字节数组，2字节=1采样点

                if (samplesPerPoint == 0) samplesPerPoint = 1;

                float[] sampledWaveform = new float[targetDataPoints];

                for (int point = 0; point < targetDataPoints; point++) {
                    double sumOfSquaresForPoint = 0;
                    int startIndex = point * samplesPerPoint * 2;    // 字节索引，每次2字节
                    int endIndex = Math.min(startIndex + samplesPerPoint * 2, length);

                    for (int j = startIndex; j < endIndex; j += 2) {
                        short sample = (short) ((waveform[j + 1] & 0xFF) << 8 | (waveform[j] & 0xFF));
                        double value = sample / 32768.0;
                        sumOfSquaresForPoint += value * value;
                    }

                    double rms = Math.sqrt(sumOfSquaresForPoint / samplesPerPoint);

                    sampledWaveform[point] = (float) rms;
                }

                notifyWaveformData(sampledWaveform);
            }
        });

        // 开始录音
        boolean success = audioRecorder.startRecording();
        if (success) {
            LogUtils.d(TAG, "Audio recording started successfully");
            notifyStateChanged(AsrState.State.RECORDING, "开始录音");
        } else {
            LogUtils.e(TAG, "Failed to start audio recording");
            notifyError("AUDIO_RECORD_FAILED", "音频录制启动失败");
            stopRecognition();
        }
    }

    /**
     * 停止录音
     */
    public void stopRecognition() {
        LogUtils.d(TAG, "Stopping ASR recognition");

        workerHandler.post(() -> {
            try {
                // 停止音频录制
                if (audioRecorder != null) {
                    audioRecorder.stopRecording();
                    audioRecorder = null;
                }

                notifyStateChanged(AsrState.State.STOPPED, "识别已停止");
                LogUtils.d(TAG, "ASR recognition stopped");
            } catch (Exception e) {
                LogUtils.e(TAG, "Error stopping recognition: " + e.getMessage());
            }
        });
    }

    /**
     * 添加监听器
     */
    public void socketOn(String event) {
        workerHandler.post(() -> {
            webSocketClient.on(event, args -> {
                String message = "";
                if (args.length > 0) {
                    message = args[0].toString();
                };
                HippyMap hippyMap = new HippyMap();
                hippyMap.pushString("event", event);
                hippyMap.pushString("message", message);
                LogUtils.d(TAG, "socketOn: " + event + " " + message);
                HostHippyMessageBridge.getInstance().onHostMessage("AbcASRSocketOn", hippyMap);
            });
        });
    }

    /**
     * 移除监听器
     */
    public void socketOff(String event) {
        workerHandler.post(() -> {
            webSocketClient.off(event, args -> {
                String message = args[0].toString();
                HippyMap hippyMap = new HippyMap();
                hippyMap.pushString("event", event);
                hippyMap.pushString("message", message);
                HostHippyMessageBridge.getInstance().onHostMessage("AbcASRSocketOff", hippyMap);
            });
        });
    }

    /**
     * 发送 Socket.IO 消息
     */
    public void sendSocketMessage(String event, HippyMap map) {
        Set<Map.Entry<String, Object>> entries = map.entrySet();
        JSONObject data = new JSONObject();
        for (Map.Entry<String, Object> entry : entries) {
            try {
                data.put(entry.getKey(), entry.getValue());
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
        }
        webSocketClient.sendSocketMessage(event, data);
    }

    /**
     * 发送事件到 JavaScript
     */
    private void invokeMethod(String eventName, HippyMap params) {
        HippyMap map = new HippyMap();
        map.pushString("event", eventName);
        map.pushMap("payload", params);
        HostHippyMessageBridge.getInstance().onHostMessage("AbcASRCallback", map);
    }

    /**
     * 通知错误
     */
    private void notifyError(String errorCode, String errorMessage) {
        HippyMap errorMap = new HippyMap();
        errorMap.pushString("errorCode", errorCode);
        errorMap.pushString("errorMessage", errorMessage);
        this.invokeMethod("onError", errorMap);
    }

    /**
     * 通知状态变化
     */
    private void notifyStateChanged(AsrState.State state, String message) {
        HippyMap stateMap = new HippyMap();
        stateMap.pushString("state", state.name());
        stateMap.pushString("message", message);
        this.invokeMethod("onStateChanged", stateMap);
    }

    /**
     * 监听来电
     */
    public void listenerCall() {
        LogUtils.d(TAG, "Setting up phone call listener");
        Context context = ContextHolder.getAppContext();
        if (context == null) {
            LogUtils.e(TAG, "Context is null, cannot setup call listener");
            notifyError("CALL_LISTENER_FAILED", "无法设置来电监听：上下文为空");
            return;
        }

        // 移除旧的监听器（如果存在）
        if (phoneStateListener != null && telephonyManager != null) {
            telephonyManager.listen(phoneStateListener, PhoneStateListener.LISTEN_NONE);
        }

        try {
            telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
            phoneStateListener = new PhoneStateListener() {
                @Override
                public void onCallStateChanged(int state, String phoneNumber) {
                    HippyMap callMap = new HippyMap();

                    switch (state) {
                        case TelephonyManager.CALL_STATE_RINGING:
                            // 来电响铃
                            callMap.pushString("state", "RINGING");
                            callMap.pushString("phoneNumber", phoneNumber);
                            invokeMethod("onCallStateChanged", callMap);
                            break;

                        case TelephonyManager.CALL_STATE_OFFHOOK:
                            // 接听电话
                            callMap.pushString("state", "OFFHOOK");
                            callMap.pushString("phoneNumber", phoneNumber);
                            invokeMethod("onCallStateChanged", callMap);
                            break;

                        case TelephonyManager.CALL_STATE_IDLE:
                            // 挂断电话或空闲状态
                            callMap.pushString("state", "IDLE");
                            callMap.pushString("phoneNumber", phoneNumber);
                            invokeMethod("onCallStateChanged", callMap);
                            break;
                    }
                }
            };

            // 注册电话状态监听器
            telephonyManager.listen(phoneStateListener, PhoneStateListener.LISTEN_CALL_STATE);

            // 通知监听器已设置成功
            HippyMap statusMap = new HippyMap();
            statusMap.pushBoolean("success", true);
            statusMap.pushString("message", "来电监听已成功设置");
            invokeMethod("onCallListenerStatus", statusMap);

        } catch (Exception e) {
            LogUtils.e(TAG, "Failed to setup call listener: " + e.getMessage());
            notifyError("CALL_LISTENER_FAILED", "设置来电监听失败: " + e.getMessage());
        }
    }

    /**
     * 释放资源
     */
    public void release() {
        LogUtils.d(TAG, "Releasing ASR manager resources");

        // 移除电话状态监听器
        if (phoneStateListener != null && telephonyManager != null) {
            telephonyManager.listen(phoneStateListener, PhoneStateListener.LISTEN_NONE);
            phoneStateListener = null;
        }

        // 等待工作线程完成
        if (workerHandler != null) {
            workerHandler.post(() -> {
                // 释放音频录制器
                if (audioRecorder != null) {
                    audioRecorder.stopRecording();
                    audioRecorder = null;
                }

                // 释放 WebSocket 客户端
                if (webSocketClient != null) {
                    webSocketClient.release();
                    webSocketClient = null;
                }
            });
        }

        // 停止工作线程
        if (workerThread != null) {
            workerThread.quitSafely();
            try {
                workerThread.join(1000);
            } catch (InterruptedException e) {
                LogUtils.e(TAG, "Error joining worker thread: " + e.getMessage());
            }
            workerThread = null;
            workerHandler = null;
        }

        LogUtils.d(TAG, "ASR manager resources released");
    }

    /**
     * 获取当前配置
     */
    public AsrConfig getConfig() {
        return config;
    }

    private void notifyWaveformData(float[] waveform) {
        HippyMap waveformMap = new HippyMap();
        HippyArray array = ArgumentUtils.fromArray(waveform);
        waveformMap.pushArray("data", array);
        this.invokeMethod("onWaveformData", waveformMap);
    }
}
