package com.abc.hippy;

import com.corundumstudio.socketio.SocketIOClient;
import io.socket.client.IO;
import io.socket.client.Socket;
import io.socket.emitter.Emitter;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.IntStream;

/**
 * Socket.IO 客户端测试用例
 * 用于测试读取 WAV 文件并通过 Socket.IO 发送到服务端进行语音转文字
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
public class SocketIOClientTest {

    private Socket socket;
    private CountDownLatch connectLatch;
    private CountDownLatch responseLatch;

    // 服务端配置
    private static final String SERVER_URL = "http://localhost:6888/cis";
    private static final String SOCKET_PATH = "/socket.io/asr";
    private static final String SUPPLIER_ID = "tencent";

    // 测试参数
    private static final String BUSINESS_ID = "test_business_123";
    private static final int BUSINESS_TYPE = 0; // 语音病历
    private static final String TASK_ID = "test_task_456";

    @BeforeEach
    public void setUp() {
        connectLatch = new CountDownLatch(1);
        responseLatch = new CountDownLatch(1);

        try {
            // 配置 Socket.IO 客户端选项
            IO.Options options = new IO.Options();
            options.path = SOCKET_PATH;
            options.transports = new String[]{"websocket"}; // 仅使用 WebSocket 传输
            options.reconnection = true;
            options.query = "supplierId=" + SUPPLIER_ID;
            // 创建 Socket.IO 客户端连接
            socket = IO.socket(URI.create(SERVER_URL), options);

            // 设置事件监听器
            setupEventListeners();

        } catch (Exception e) {
            log.error("Failed to setup Socket.IO client", e);
            throw new RuntimeException(e);
        }
    }

    @AfterEach
    public void tearDown() {
        if (socket != null) {
            socket.disconnect();
            socket.close();
        }
    }

    /**
     * 设置 Socket.IO 事件监听器
     */
    private void setupEventListeners() {
        // 连接成功事件
        socket.on(Socket.EVENT_CONNECT, new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                log.info("Socket.IO connected successfully");
                connectLatch.countDown();
            }
        });

        // 连接失败事件
        socket.on(Socket.EVENT_CONNECT_ERROR, new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                log.error("Socket.IO connection error: {}", args.length > 0 ? args[0] : "Unknown error");
                connectLatch.countDown();
            }
        });

        // 断开连接事件
        socket.on(Socket.EVENT_DISCONNECT, new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                log.info("Socket.IO disconnected: {}", args.length > 0 ? args[0] : "Unknown reason");
            }
        });

        // ASR 结果事件
        socket.on("asr-result", new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                log.info("Received ASR result: {}", args.length > 0 ? args[0] : "No data");
            }
        });

        // ASR 完成事件
        socket.on("asr-completed", new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                log.info("ASR completed: {}", args.length > 0 ? args[0] : "No data");
                responseLatch.countDown();
            }
        });

        // 录音开始确认事件
        socket.on("recording-started", new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                log.info("Recording started confirmation: {}", args.length > 0 ? args[0] : "No data");
            }
        });

        // 加入房间确认事件
        socket.on("join", new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                log.info("Joined room confirmation: {}", args.length > 0 ? args[0] : "No data");
            }
        });

        // 错误事件
        socket.on("error", new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                log.error("Socket.IO error: {}", args.length > 0 ? args[0] : "Unknown error");
            }
        });
    }

    /**
     * 测试读取 WAV 文件并发送到服务端进行语音转文字
     */
    @Test
    public void testSendWavFileForASR() throws Exception {
        log.info("Starting WAV file ASR test...");

        // 连接到服务端
        socket.connect();

        // 等待连接建立
        boolean connected = connectLatch.await(10, TimeUnit.SECONDS);
        if (!connected) {
            throw new RuntimeException("Failed to connect to Socket.IO server within 10 seconds");
        }

        // 发送开始录音事件
        socket.emit("start-recording", createStartRecordingData());
        log.info("Sent start-recording event");

        // 等待一下让服务端处理
        Thread.sleep(1000);

        // 读取并发送 WAV 文件
        sendWavFile();

        // 发送结束信号
        socket.emit("JSON", createEndSignal());
        log.info("Sent end signal");

        // 等待 ASR 结果
        boolean responseReceived = responseLatch.await(30, TimeUnit.SECONDS);
        if (!responseReceived) {
            log.warn("No ASR response received within 30 seconds");
        }

        log.info("WAV file ASR test completed");
    }

    /**
     * 创建开始录音的数据
     */
    private Object createStartRecordingData() {
        return new Object() {
            public final String event = "recording-start";
            public final String businessId = BUSINESS_ID;
            public final int businessType = BUSINESS_TYPE;
            public final String taskId = TASK_ID;
        };
    }

    /**
     * 创建结束信号
     */
    private Object createEndSignal() {
        return new Object() {
            public final String type = "end";
        };
    }

    /**
     * 读取并发送 WAV 文件
     */
    private void sendWavFile() throws IOException {
        // 读取 assets 目录下的 WAV 文件
        ClassPathResource resource = new ClassPathResource("static/assets/voice_record_3821425508204232704.wav");

        if (!resource.exists()) {
            throw new RuntimeException("WAV file not found: " + resource.getPath());
        }

        log.info("Reading WAV file: {}", resource.getPath());

        try (InputStream inputStream = resource.getInputStream()) {
            byte[] wavData = IOUtils.toByteArray(inputStream);
            log.info("Read WAV file, size: {} bytes", wavData.length);

            // 分块发送音频数据，模拟实时音频流
            int chunkSize = 1024; // 每次发送 1KB
            int totalChunks = (wavData.length + chunkSize - 1) / chunkSize;

            log.info("Sending WAV data in {} chunks of {} bytes each", totalChunks, chunkSize);

            for (int i = 0; i < totalChunks; i++) {
                int start = i * chunkSize;
                int end = Math.min(start + chunkSize, wavData.length);
                int dataLength = end - start;
                Byte[] chunk = IntStream.range(0, dataLength)
                        .boxed()
                        .map(index -> wavData[start + index])
                        .toArray(Byte[]::new);
                // 发送音频数据块
                socket.emit("audio-data", (Object) chunk);

                if (i % 10 == 0) { // 每10个块打印一次进度
                    log.info("Sent chunk {}/{} ({} bytes)", i + 1, totalChunks, chunk.length);
                }

                // 模拟实时发送，稍微延迟
                try {
                    Thread.sleep(50); // 50ms 延迟
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }

            log.info("Finished sending all {} chunks", totalChunks);
        }
    }
}
